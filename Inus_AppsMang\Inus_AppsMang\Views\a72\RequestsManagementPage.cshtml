@{
    ViewBag.Title = "إدارة طلبات المخزون";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/Content/RequestsManagement.css" rel="stylesheet" />
<link href="~/Content/PrintStyles.css" rel="stylesheet" />
<style>
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .badge-secondary { background-color: #6c757d; color: white; }
    .badge-warning { background-color: #ffc107; color: #212529; }
    .badge-success { background-color: #198754; color: white; }
    .badge-danger { background-color: #dc3545; color: white; }
    .badge-info { background-color: #0dcaf0; color: #212529; }
    .badge-primary { background-color: #0d6efd; color: white; }
    .bg-purple { background-color: #6f42c1; color: white; }
    .text-purple { color: #6f42c1; }
    .bi-arrow-bar-right::before { content: "\f128"; }
</style>

<div class="container-fluid requests-container cairo animate-fade-in" ng-controller="a72" as="ctrl">

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="cairo">إدارة طلبات المخزون</h5>
                        </div>
                        <div class="col-6 d-print-none">
                            <button class="btn btn-primary col-4 float-end m-1 cairo" ng-click="ctrl.goHome()">الرئيسية</button>
                            @*<button class="btn btn-secondary col-4 bi-arrow-bar-right float-end m-1 cairo" ng-click="ctrl.Back()"> رجوع</button>*@
                            @*<button class="btn btn-success float-end m-1 cairo" ng-click="ctrl.RefreshData()"> <span class="bi-arrow-clockwise"></span></button>*@
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted cairo">مراجعة وإدارة طلبات التسلسلات من الوكالات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card cairo" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="stats-number">{{ctrl.statistics.NewRequests || 0}}</div>
                <div class="stats-label">
                    <i class="bi bi-plus-circle me-2"></i>
                    طلبات جديدة
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card cairo" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="stats-number">{{ctrl.statistics.UnderReview || 0}}</div>
                <div class="stats-label">
                    <i class="bi bi-clock me-2"></i>
                    قيد المراجعة
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card cairo" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                <div class="stats-number">{{ctrl.statistics.Approved || 0}}</div>
                <div class="stats-label">
                    <i class="bi bi-check-circle me-2"></i>
                    معتمدة
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card cairo" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="stats-number">{{ctrl.statistics.Executed || 0}}</div>
                <div class="stats-label">
                    <i class="bi bi-arrow-right-circle me-2"></i>
                    منفذة
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Panel -->
    <div class="search-panel cairo">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label class="form-label fw-bold cairo">البحث برقم الطلب</label>
                <input type="text" class="form-control cairo" ng-model="ctrl.searchTerm"
                       placeholder="أدخل رقم الطلب" ng-change="ctrl.resetPageAndSearch()">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label fw-bold cairo">حالة الطلب</label>
                <select class="form-select cairo" ng-model="ctrl.statusFilter" ng-change="ctrl.resetPageAndSearch()">
                    <option value="">جميع الحالات</option>
                    <option value="1">جديد</option>
                    <option value="2">قيد المراجعة</option>
                    <option value="3">معتمد</option>
                    <option value="4">مرفوض</option>
                    <option value="5">منفذ</option>
                    <option value="6">مستلم</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label fw-bold cairo">من تاريخ</label>
                <input type="date" class="form-control cairo" ng-model="ctrl.dateFrom" ng-change="ctrl.resetPageAndSearch()">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label fw-bold cairo">إلى تاريخ</label>
                <input type="date" class="form-control cairo" ng-model="ctrl.dateTo" ng-change="ctrl.resetPageAndSearch()">
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <button class="btn btn-primary me-2 cairo" ng-click="ctrl.resetPageAndSearch()">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
                <button class="btn btn-outline-secondary cairo" ng-click="ctrl.clearFilters()">
                    <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين
                </button>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-info cairo" ng-click="ctrl.resetPageAndSearch()">
                    <i class="bi bi-search me-2"></i>بحث متقدم
                </button>
            </div>
        </div>
    </div>

    <!-- Requests Table -->
    <div class="requests-table">
        <table class="table table-hover mb-0 cairo">
            <thead>
                <tr>
                    <th class="cairo">رقم الطلب</th>
                    <th class="cairo">الوكالة</th>
                    <th class="cairo">توع التأمين</th>
                    <th class="cairo">نوع الطلب</th>
                    <th class="cairo"> العدد</th>
                    <th class="cairo">الحالة</th>
                    <th class="cairo">تاريخ الإنشاء</th>
                    <th class="cairo">آخر تحديث</th>
                    <th class="cairo">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="request in ctrl.requestsList" ng-class="{'table-warning': request.Status === 1}">
                    <td class="fw-bold cairo">{{request.RequestNumber}}</td>
                    <td class="cairo">{{request.AgencyName}}</td>
                    <td class="cairo">{{request.SysName}}</td>
                    <td class="cairo">{{request.RequestType}}</td>
                    <td class="cairo">{{request.RequestedQuantity}}</td>
                    <td class="cairo">
                        <span class="status-badge" ng-class="ctrl.getStatusClass(request.Status)">
                            {{ctrl.getStatusName(request.Status)}}
                        </span>
                    </td>
                    <td class="cairo">{{request.RequestDate | date:'dd/MM/yyyy HH:mm'}}</td>
                    <td class="cairo">{{request.StatusDate | date:'dd/MM/yyyy HH:mm'}}</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-info btn-sm" ng-click="ctrl.viewRequestDetails(request)" title="عرض التفاصيل" data-bs-toggle="modal" data-bs-target="#requestDetailsModal">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" ng-click="ctrl.startReview(request)" data-bs-toggle="modal" data-bs-target="#reviewRequestModal"
                                    ng-if="request.Status === 1 || request.Status === 2" title="مراجعة">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-success btn-sm" ng-click="ctrl.executeRequest(request)" data-bs-toggle="modal" data-bs-target="#executeRequestModal"
                                    ng-if="request.Status === 3" title="تنفيذ">
                                <i class="bi bi-play-fill"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr ng-if="!ctrl.requestsList || ctrl.requestsList.length === 0">
                    <td colspan="8" class="text-center text-muted py-4 cairo">
                        <i class="bi bi-inbox display-4 d-block mb-3"></i>
                        لا توجد طلبات متاحة
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

   
    <div class="d-flex justify-content-between align-items-center mt-3 cairo" ng-if="ctrl.requestsList && ctrl.requestsList.length > 0">
        <div class="text-muted cairo">
            عرض {{ctrl.requestsList.length}} من أصل {{ctrl.totalItems}} طلب
            <span ng-if="ctrl.totalPages > 1">(صفحة {{ctrl.currentPage}} من {{ctrl.totalPages}})</span>
        </div>
        <nav aria-label="صفحات الطلبات" ng-if="ctrl.totalPages > 1">
            <ul class="pagination">
                <li class="page-item" ng-class="{disabled: ctrl.currentPage <= 1}">
                    <button class="page-link cairo" ng-click="ctrl.previousPage()" ng-disabled="ctrl.currentPage <= 1">السابق</button>
                </li>
                <li class="page-item active">
                    <span class="page-link cairo">{{ctrl.currentPage}}</span>
                </li>
                <li class="page-item" ng-class="{disabled: ctrl.currentPage >= ctrl.totalPages}">
                    <button class="page-link cairo" ng-click="ctrl.nextPage()" ng-disabled="ctrl.currentPage >= ctrl.totalPages">التالي</button>
                </li>
            </ul>
        </nav>
    </div>

</div>


<div class="modal fade" id="requestDetailsModal" tabindex="-1" aria-labelledby="requestDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title cairo" id="requestDetailsModalLabel">
                    <i class="bi bi-info-circle me-2"></i>تفاصيل الطلب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <div class="row" ng-if="ctrl.selectedRequest">
                    <div class="col-md-6 mb-3">
                        <strong>رقم الطلب:</strong> {{ctrl.selectedRequest.RequestNumber}}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الوكالة:</strong> {{ctrl.selectedRequest.AgencyName}}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>نوع الطلب:</strong> {{ctrl.selectedRequest.RequestType}}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>العدد:</strong> {{ctrl.selectedRequest.RequestedQuantity}}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>معدل الإستهلاك لأأخر تلات أشهر:</strong> 0
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>المتاح:</strong> {{ctrl.selectedRequest.InventDocsAvilable}}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الحالة:</strong>
                        <span class="status-badge" ng-class="ctrl.getStatusClass(ctrl.selectedRequest.Status)">
                            {{ctrl.getStatusName(ctrl.selectedRequest.Status)}}
                        </span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>تاريخ الإنشاء:</strong> {{ctrl.selectedRequest.RequestDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>آخر تحديث:</strong> {{ctrl.selectedRequest.StatusDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-12 mb-3" ng-if="ctrl.selectedRequest.Notes">
                        <strong>الملاحظات:</strong>
                        <p class="border rounded p-3 bg-light cairo">{{ctrl.selectedRequest.Notes}}</p>
                    </div>
                    <div class="col-12 mb-3" ng-if="ctrl.selectedRequest.ChangeReason">
                        <strong>سبب الطلب:</strong>
                        <p class="border rounded p-3 bg-warning bg-opacity-10 cairo">{{ctrl.selectedRequest.ChangeReason}}</p>
                    </div>
                    <!-- بيانات التسلسل المعتمد - تظهر فقط للطلبات المعتمدة -->
                    <div class="col-12 mb-3" ng-if="ctrl.selectedRequest.Status === 3">
                        <h6 class="text-success"><i class="bi bi-check-circle me-2"></i>بيانات التسلسل المعتمد</h6>
                        <div class="row" ng-if="ctrl.selectedRequest.StartSequence">
                            <div class="col-md-4">
                                <strong>بداية التسلسل:</strong>
                                <span class="badge bg-info ms-2">{{ctrl.selectedRequest.StartSequence}}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>العدد المعتمد:</strong>
                                <span class="badge bg-success ms-2">{{ctrl.selectedRequest.Quantity}}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>نهاية التسلسل:</strong>
                                <span class="badge bg-info ms-2">{{ctrl.selectedRequest.EndSequence}}</span>
                            </div>
                        </div>
                        <div class="alert alert-warning" ng-if="!ctrl.selectedRequest.StartSequence">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            لم يتم تحديد بيانات التسلسل لهذا الطلب المعتمد
                        </div>
                    </div>
                    <!-- معلومات المراجع للطلبات المعتمدة -->
                    <div class="col-12 mb-3" ng-if="ctrl.selectedRequest.Status === 3">
                        <h6 class="text-info"><i class="bi bi-person-check me-2"></i>معلومات المراجعة</h6>
                        <div class="row" ng-if="ctrl.selectedRequest.ReviewedBy">
                            <div class="col-md-6">
                                <strong>مراجع الطلب:</strong>
                                <span class="text-primary">{{ctrl.selectedRequest.ReviewedBy}}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>تاريخ المراجعة:</strong>
                                <span class="text-primary">{{ctrl.selectedRequest.ReviewDate | date:'dd/MM/yyyy HH:mm'}}</span>
                            </div>
                        </div>
                        <div class="row mt-2" ng-if="ctrl.selectedRequest.ReviewNotes">
                            <div class="col-12">
                                <strong>ملاحظات المراجعة:</strong>
                                <p class="border rounded p-2 bg-light mt-1 cairo">{{ctrl.selectedRequest.ReviewNotes}}</p>
                            </div>
                        </div>
                        <div class="alert alert-warning" ng-if="!ctrl.selectedRequest.ReviewedBy">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            لم يتم تحديد مراجع لهذا الطلب المعتمد
                            <br><small>Status: {{ctrl.selectedRequest.Status}} | ReviewedByID: {{ctrl.selectedRequest.ReviewedByID}}</small>
                        </div>
                    </div>
                    <!-- معلومات التنفيذ للطلبات المنفذة -->
                    <div class="col-12 mb-3" ng-if="ctrl.selectedRequest.Status === 5">
                        <h6 class="text-purple"><i class="bi bi-play-fill me-2"></i>معلومات التنفيذ</h6>
                        <div class="row" ng-if="ctrl.selectedRequest.ExecutedBy">
                            <div class="col-md-6">
                                <strong>منفذ الطلب:</strong>
                                <span class="text-primary">{{ctrl.selectedRequest.ExecutedBy}}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>تاريخ التنفيذ:</strong>
                                <span class="text-primary">{{ctrl.selectedRequest.ExecutedDate | date:'dd/MM/yyyy HH:mm'}}</span>
                            </div>
                        </div>
                        <div class="row mt-2" ng-if="ctrl.selectedRequest.ExecutionNotes">
                            <div class="col-12">
                                <strong>ملاحظات التنفيذ:</strong>
                                <p class="border rounded p-2 bg-light mt-1 cairo">{{ctrl.selectedRequest.ExecutionNotes}}</p>
                            </div>
                        </div>
                        <div class="alert alert-warning" ng-if="!ctrl.selectedRequest.ExecutedBy">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            لم يتم تحديد منفذ لهذا الطلب المنفذ
                            <br><small>Status: {{ctrl.selectedRequest.Status}} | ExecutedBy: {{ctrl.selectedRequest.ExecutedBy}}</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info cairo" ng-click="ctrl.printApprovedRequestReport()"
                        ng-if="ctrl.selectedRequest.Status === 3" title="طباعة تقرير المراجعة">
                    <i class="bi bi-printer me-2"></i>طباعة المراجعة
                </button>
                <button type="button" class="btn btn-success cairo" ng-click="ctrl.printExecutedRequestReport()"
                        ng-if="ctrl.selectedRequest.Status === 5" title="طباعة تقرير التنفيذ">
                    <i class="bi bi-printer me-2"></i>طباعة التنفيذ
                </button>
                <button type="button" class="btn btn-secondary cairo" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="reviewRequestModal" tabindex="-1" aria-labelledby="reviewRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title cairo" id="reviewRequestModalLabel">
                    <i class="bi bi-pencil-square me-2"></i>مراجعة الطلب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <form name="reviewForm">
                    <div class="mb-3">
                        <label class="form-label fw-bold cairo">رقم الطلب</label>
                        <input type="text" class="form-control cairo" ng-model="ctrl.reviewData.RequestNumber" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold cairo">قرار المراجعة</label>
                        <select class="form-select cairo" ng-model="ctrl.reviewData.IsApproved" required>
                            <option value="">اختر القرار</option>
                            <option value="true">موافقة</option>
                            <option value="false">رفض</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold cairo">ملاحظات الإدارة</label>
                        <textarea class="form-control cairo" ng-model="ctrl.reviewData.ReviewNotes" rows="4"
                                  placeholder="أدخل ملاحظاتك هنا..."></textarea>
                    </div>
                    <div class="mb-3" ng-if="ctrl.reviewData.IsApproved === 'false'">
                        <label class="form-label fw-bold cairo">سبب الرفض</label>
                        <textarea class="form-control cairo" ng-model="ctrl.reviewData.RejectionReason" rows="3"
                                  placeholder="أدخل سبب الرفض..."></textarea>
                    </div>
                    <div class="mb-3" ng-if="ctrl.reviewData.IsApproved === 'true'">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label fw-bold cairo">بداية التسلسل</label>
                                <input type="number" class="form-control cairo" ng-model="ctrl.reviewData.StartSequence" 
                                       ng-change="ctrl.calculateEndSequence()" placeholder="أدخل بداية التسلسل">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold cairo">العدد</label>
                                <input type="number" class="form-control cairo" ng-model="ctrl.reviewData.Quantity" 
                                       ng-change="ctrl.calculateEndSequence()" placeholder="أدخل العدد">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold cairo">نهاية التسلسل</label>
                                <input type="number" class="form-control cairo" ng-model="ctrl.reviewData.EndSequence" 
                                       readonly placeholder="سيتم الحساب تلقائياً">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success cairo" ng-click="ctrl.a72004(ctrl.reviewData.IsApproved === 'true')"
                        ng-disabled="!ctrl.isConfirmButtonEnabled()">
                    <i class="bi bi-check-lg me-2"></i>تأكيد المراجعة
                </button>
                <button type="button" class="btn btn-info cairo" ng-click="ctrl.printReviewReport()" 
                        ng-disabled="!ctrl.isPrintButtonEnabled()">
                    <i class="bi bi-printer me-2"></i>طباعة تقرير المراجعة
                </button>
                <button type="button" class="btn btn-warning cairo" ng-click="ctrl.printReportDirect()" 
                        ng-disabled="!ctrl.isPrintButtonEnabled()">
                    <i class="bi bi-printer me-2"></i>طباعة مباشرة
                </button>
                <button type="button" class="btn btn-secondary cairo" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="executeRequestModal" tabindex="-1" aria-labelledby="executeRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title cairo" id="executeRequestModalLabel">
                    <i class="bi bi-play-fill me-2"></i>تنفيذ الطلب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo">
                <form name="executeForm">
                    <div class="mb-3">
                        <label class="form-label fw-bold cairo">رقم الطلب</label>
                        <input type="text" class="form-control cairo" ng-model="ctrl.executionData.RequestNumber" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold cairo">ملاحظات التنفيذ</label>
                        <textarea class="form-control cairo" ng-model="ctrl.executionData.ExecutionNotes" rows="4"
                                  placeholder="أدخل ملاحظات التنفيذ..."></textarea>
                    </div>
                    <div class="alert alert-info cairo">
                        <i class="bi bi-info-circle me-2"></i>
                        سيتم تنفيذ الطلب وتغيير حالته إلى "منفذ"
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary cairo" ng-click="ctrl.a72005()">
                    <i class="bi bi-play-fill me-2"></i>تأكيد التنفيذ
                </button>
                <button type="button" class="btn btn-secondary cairo" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Review Report Print - تحسين المودال -->
<div class="modal fade" id="reviewReportModal" tabindex="-1" aria-labelledby="reviewReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title cairo" id="reviewReportModalLabel">
                    <i class="bi bi-printer me-2"></i>تقرير مراجعة الطلب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo" id="reviewReportContent">
                <!-- ترويسة التقرير -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h3 class="fw-bold mb-2">تقرير مراجعة طلب التسلسلات</h3>
                        <p class="text-muted mb-0">تاريخ الطباعة: {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm'}}</p>
                        <hr class="my-3">
                    </div>
                </div>
                
                <!-- معلومات الطلب الأساسية -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الطلب:</strong> {{ctrl.reviewData.RequestNumber}}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ المراجعة:</strong> {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الوكالة:</strong> {{ctrl.selectedRequest.AgencyName}}
                    </div>
                    <div class="col-md-6">
                        <strong>نوع التأمين:</strong> {{ctrl.selectedRequest.SysName}}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>تاريخ إنشاء الطلب:</strong> {{ctrl.selectedRequest.RequestDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>مقدم الطلب:</strong> {{ctrl.selectedRequest.RequestedBy}}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>نوع الطلب:</strong> {{ctrl.selectedRequest.RequestType}}
                    </div>
                    <div class="col-md-6">
                        <strong>العدد المطلوب:</strong> {{ctrl.selectedRequest.RequestedQuantity}}
                    </div>
                </div>
                
                <!-- بيانات التسلسل (إذا تمت الموافقة) -->
                <div class="row mb-3" ng-if="ctrl.reviewData.IsApproved === 'true'">
                    <div class="col-md-4">
                        <strong>بداية التسلسل:</strong> {{ctrl.reviewData.StartSequence}}
                    </div>
                    <div class="col-md-4">
                        <strong>العدد:</strong> {{ctrl.reviewData.Quantity}}
                    </div>
                    <div class="col-md-4">
                        <strong>نهاية التسلسل:</strong> {{ctrl.reviewData.EndSequence}}
                    </div>
                </div>
                
                <!-- قرار المراجعة -->
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>قرار المراجعة:</strong>
                        <span class="badge ms-2" ng-class="ctrl.reviewData.IsApproved === 'true' ? 'bg-success' : 'bg-danger'">
                            {{ctrl.reviewData.IsApproved === 'true' ? 'موافقة' : 'رفض'}}
                        </span>
                    </div>
                </div>
                
                <!-- ملاحظات الإدارة -->
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>ملاحظات الإدارة:</strong>
                        <div class="border rounded p-3 bg-light mt-2">
                            {{ctrl.reviewData.ReviewNotes}}
                        </div>
                    </div>
                </div>
                
                <!-- سبب الرفض (إذا كان مرفوض) -->
                <div class="row mb-3" ng-if="ctrl.reviewData.IsApproved === 'false' && ctrl.reviewData.RejectionReason">
                    <div class="col-12">
                        <strong>سبب الرفض:</strong>
                        <div class="border rounded p-3 bg-warning bg-opacity-10 mt-2">
                            {{ctrl.reviewData.RejectionReason}}
                        </div>
                    </div>
                </div>
                
                <!-- معلومات المراجع -->
                <div class="row mb-3">
                    <div class="col-12" ng-if="ctrl.selectedRequest.ReviewedBy">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>مراجع الطلب:</strong> {{ctrl.selectedRequest.ReviewedBy}}
                            </div>
                            <div class="col-md-6">
                                <strong>تاريخ المراجعة:</strong> {{ctrl.selectedRequest.ReviewDate | date:'dd/MM/yyyy HH:mm'}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12" ng-if="!ctrl.selectedRequest.ReviewedBy">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            معلومات المراجع غير متاحة
                        </div>
                    </div>
                </div>
                
                <!-- تذييل التقرير -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة طلبات المخزون<br>
                                تاريخ الإنشاء: {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm:ss'}}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success cairo" ng-click="ctrl.saveReviewData()">
                    <i class="bi bi-save me-2"></i>حفظ بيانات المراجعة
                </button>
                <button type="button" class="btn btn-primary cairo" ng-click="ctrl.printReport()">
                    <i class="bi bi-printer me-2"></i>طباعة في نافذة جديدة
                </button>
                <button type="button" class="btn btn-info cairo" ng-click="ctrl.printReportDirect()">
                    <i class="bi bi-printer me-2"></i>طباعة مباشرة
                </button>
                <button type="button" class="btn btn-secondary cairo" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- End of reviewReportModal -->

<!-- Modal for Approved Request Print Report -->
<div class="modal fade" id="approvedRequestReportModal" tabindex="-1" aria-labelledby="approvedRequestReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title cairo" id="approvedRequestReportModalLabel">
                    <i class="bi bi-printer me-2"></i>تقرير الطلب المعتمد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo" id="approvedRequestReportContent">
                <!-- ترويسة التقرير -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h3 class="fw-bold mb-2">تقرير الطلب المعتمد</h3>
                        <p class="text-muted mb-0">تاريخ الطباعة: {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm'}}</p>
                        <hr class="my-3">
                    </div>
                </div>

                <!-- معلومات الطلب الأساسية -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الطلب:</strong> {{ctrl.selectedRequest.RequestNumber}}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الاعتماد:</strong> {{ctrl.selectedRequest.ReviewDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الوكالة:</strong> {{ctrl.selectedRequest.AgencyName}}
                    </div>
                    <div class="col-md-6">
                        <strong>نوع التأمين:</strong> {{ctrl.selectedRequest.SystemName}}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>تاريخ إنشاء الطلب:</strong> {{ctrl.selectedRequest.RequestDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>مقدم الطلب:</strong> {{ctrl.selectedRequest.RequestedBy}}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>نوع الطلب:</strong> {{ctrl.selectedRequest.RequestType}}
                    </div>
                    <div class="col-md-6">
                        <strong>العدد المطلوب:</strong> {{ctrl.selectedRequest.RequestedQuantity}}
                    </div>
                </div>

                <!-- بيانات التسلسل المعتمد -->
                <div class="row mb-3">
                    <div class="col-12 mb-2">
                        <h6 class="text-primary"><i class="bi bi-list-ol me-2"></i>بيانات التسلسل المعتمد</h6>
                        <hr>
                    </div>
                    <div class="row" ng-if="ctrl.selectedRequest.StartSequence">
                        <div class="col-md-4">
                            <strong>بداية التسلسل:</strong>
                            <span class="badge bg-info">{{ctrl.selectedRequest.StartSequence}}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>العدد المعتمد:</strong>
                            <span class="badge bg-success">{{ctrl.selectedRequest.Quantity}}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>نهاية التسلسل:</strong>
                            <span class="badge bg-info">{{ctrl.selectedRequest.EndSequence}}</span>
                        </div>
                    </div>
                    <div class="alert alert-warning" ng-if="!ctrl.selectedRequest.StartSequence">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        لم يتم تحديد بيانات التسلسل لهذا الطلب المعتمد
                    </div>
                </div>

                <!-- حالة الطلب -->
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>حالة الطلب:</strong>
                        <span class="badge bg-success ms-2">معتمد</span>
                    </div>
                </div>

                <!-- ملاحظات المراجعة -->
                <div class="row mb-3" ng-if="ctrl.selectedRequest.ReviewNotes">
                    <div class="col-12">
                        <strong>ملاحظات المراجعة:</strong>
                        <div class="border rounded p-3 bg-light mt-2">
                            {{ctrl.selectedRequest.ReviewNotes}}
                        </div>
                    </div>
                </div>

                <!-- معلومات المراجع -->
                <div class="row mb-3">
                    <div class="col-12" ng-if="ctrl.selectedRequest.ReviewedBy">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>مراجع الطلب:</strong> {{ctrl.selectedRequest.ReviewedBy}}
                            </div>
                            <div class="col-md-6">
                                <strong>تاريخ المراجعة:</strong> {{ctrl.selectedRequest.ReviewDate | date:'dd/MM/yyyy HH:mm'}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12" ng-if="!ctrl.selectedRequest.ReviewedBy">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            معلومات المراجع غير متاحة
                        </div>
                    </div>
                </div>

                <!-- تذييل التقرير -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة طلبات المخزون<br>
                                تاريخ الإنشاء: {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm:ss'}}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary cairo" ng-click="ctrl.printApprovedReport()">
                    <i class="bi bi-printer me-2"></i>طباعة في نافذة جديدة
                </button>
                <button type="button" class="btn btn-info cairo" ng-click="ctrl.printApprovedReportDirect()">
                    <i class="bi bi-printer me-2"></i>طباعة مباشرة
                </button>
                <button type="button" class="btn btn-secondary cairo" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- End of approvedRequestReportModal -->

<!-- Modal for Executed Request Print Report -->
<div class="modal fade" id="executedRequestReportModal" tabindex="-1" aria-labelledby="executedRequestReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title cairo" id="executedRequestReportModalLabel">
                    <i class="bi bi-printer me-2"></i>تقرير الطلب المنفذ
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cairo" id="executedRequestReportContent">
                <!-- ترويسة التقرير -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h3 class="fw-bold mb-2">تقرير الطلب المنفذ</h3>
                        <p class="text-muted mb-0">تاريخ الطباعة: {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm'}}</p>
                        <hr class="my-3">
                    </div>
                </div>

                <!-- معلومات الطلب الأساسية -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الطلب:</strong> {{ctrl.selectedRequest.RequestNumber}}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ التنفيذ:</strong> {{ctrl.selectedRequest.ExecutedDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الوكالة:</strong> {{ctrl.selectedRequest.AgencyName}}
                    </div>
                    <div class="col-md-6">
                        <strong>نوع التأمين:</strong> {{ctrl.selectedRequest.SystemName}}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>تاريخ إنشاء الطلب:</strong> {{ctrl.selectedRequest.RequestDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>مقدم الطلب:</strong> {{ctrl.selectedRequest.RequestedBy}}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>نوع الطلب:</strong> {{ctrl.selectedRequest.RequestType}}
                    </div>
                    <div class="col-md-6">
                        <strong>العدد المطلوب:</strong> {{ctrl.selectedRequest.RequestedQuantity}}
                    </div>
                </div>

                <!-- بيانات التسلسل المنفذ -->
                <div class="row mb-3">
                    <div class="col-12 mb-2">
                        <h6 class="text-primary"><i class="bi bi-list-ol me-2"></i>بيانات التسلسل المنفذ</h6>
                        <hr>
                    </div>
                    <div class="row" ng-if="ctrl.selectedRequest.StartSequence">
                        <div class="col-md-4">
                            <strong>بداية التسلسل:</strong>
                            <span class="badge bg-info">{{ctrl.selectedRequest.StartSequence}}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>العدد المنفذ:</strong>
                            <span class="badge bg-success">{{ctrl.selectedRequest.Quantity}}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>نهاية التسلسل:</strong>
                            <span class="badge bg-info">{{ctrl.selectedRequest.EndSequence}}</span>
                        </div>
                    </div>
                    <div class="alert alert-warning" ng-if="!ctrl.selectedRequest.StartSequence">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        لم يتم تحديد بيانات التسلسل لهذا الطلب المنفذ
                    </div>
                </div>

                <!-- حالة الطلب -->
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>حالة الطلب:</strong>
                        <span class="badge bg-purple ms-2">منفذ</span>
                    </div>
                </div>

                <!-- معلومات المراجعة -->
                <div class="row mb-3" ng-if="ctrl.selectedRequest.ReviewedBy">
                    <div class="col-12 mb-2">
                        <h6 class="text-success"><i class="bi bi-check-circle me-2"></i>معلومات المراجعة</h6>
                        <hr>
                    </div>
                    <div class="col-md-6">
                        <strong>مراجع الطلب:</strong> {{ctrl.selectedRequest.ReviewedBy}}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ المراجعة:</strong> {{ctrl.selectedRequest.ReviewDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-12 mt-2" ng-if="ctrl.selectedRequest.ReviewNotes">
                        <strong>ملاحظات المراجعة:</strong>
                        <div class="border rounded p-3 bg-light mt-2">
                            {{ctrl.selectedRequest.ReviewNotes}}
                        </div>
                    </div>
                </div>

                <!-- معلومات التنفيذ -->
                <div class="row mb-3">
                    <div class="col-12 mb-2">
                        <h6 class="text-purple"><i class="bi bi-play-fill me-2"></i>معلومات التنفيذ</h6>
                        <hr>
                    </div>
                    <div class="col-md-6">
                        <strong>منفذ الطلب:</strong> {{ctrl.selectedRequest.ExecutedBy}}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ التنفيذ:</strong> {{ctrl.selectedRequest.ExecutedDate | date:'dd/MM/yyyy HH:mm'}}
                    </div>
                    <div class="col-12 mt-2" ng-if="ctrl.selectedRequest.ExecutionNotes">
                        <strong>ملاحظات التنفيذ:</strong>
                        <div class="border rounded p-3 bg-light mt-2">
                            {{ctrl.selectedRequest.ExecutionNotes}}
                        </div>
                    </div>
                </div>

                <!-- تذييل التقرير -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة طلبات المخزون<br>
                                تاريخ الإنشاء: {{ctrl.currentDate | date:'dd/MM/yyyy HH:mm:ss'}}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary cairo" ng-click="ctrl.printExecutedReport()">
                    <i class="bi bi-printer me-2"></i>طباعة في نافذة جديدة
                </button>
                <button type="button" class="btn btn-info cairo" ng-click="ctrl.printExecutedReportDirect()">
                    <i class="bi bi-printer me-2"></i>طباعة مباشرة
                </button>
                <button type="button" class="btn btn-secondary cairo" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- End of executedRequestReportModal -->

</div>
<!-- End of container-fluid -->