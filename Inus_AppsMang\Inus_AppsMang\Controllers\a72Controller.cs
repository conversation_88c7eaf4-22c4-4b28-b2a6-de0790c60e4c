using Inus_AppsMang.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web.Mvc;

namespace Inus_AppsMang.Controllers
{
    public class a72Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();

        // الصفحة الرئيسية لإدارة الطلبات
        public ActionResult a72000()
        {
            try
            {
                // لا نحتاج للوصول لقاعدة البيانات عند تحميل الصفحة
                // فقط نعيد الصفحة مباشرة
                return View("RequestsManagementPage");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في السجل
                System.Diagnostics.Debug.WriteLine("Error in a72000: " + ex.Message);
                
                // إرجاع HTML مباشر بدلاً من View
                return Content(@"
                    <!DOCTYPE html>
                    <html lang='ar' dir='rtl'>
                    <head>
                        <meta charset='utf-8'>
                        <title>إدارة طلبات المخزون</title>
                        <link href='/Content/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
                        <link href='/Content/RequestsManagement.css' rel='stylesheet'>
                        <style>
                            body { font-family: 'Cairo', sans-serif; }
                            .loading { text-align: center; padding: 50px; }
                        </style>
                    </head>
                    <body>
                        <div class='container-fluid'>
                            <div class='row'>
                                <div class='col-12'>
                                    <div class='card'>
                                        <div class='card-header'>
                                            <h5>إدارة طلبات المخزون</h5>
                                        </div>
                                        <div class='card-body'>
                                            <div class='loading'>
                                                <h4>جاري تحميل النظام...</h4>
                                                <p>يرجى الانتظار قليلاً</p>
                                                <button class='btn btn-primary' onclick='location.reload()'>إعادة تحميل</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <script src='/Scripts/angular.min.js'></script>
                        <script src='/FrontEnd/a72.js'></script>
                    </body>
                    </html>
                ", "text/html");
            }
        }

        // اختبار بسيط للتأكد من عمل الكونترولر
        public ActionResult Test()
        {
            return Content("Controller is working!");
        }

        // اختبار قاعدة البيانات
        public ActionResult TestDB()
        {
            try
            {
                // اختبار الاتصال
                var connectionState = db.Database.Connection.State;
                if (connectionState != System.Data.ConnectionState.Open)
                {
                    db.Database.Connection.Open();
                }
                
                // اختبار استعلام بسيط
                var count = db.UnifiedRequests.Count();
                
                // اختبار الجداول المرتبطة
                var agenciesCount = db.Agency.Count();
                var requestTypesCount = db.RequestTypes.Count();
                
                return Content($"Database is working!<br/>" +
                              $"Total requests: {count}<br/>" +
                              $"Total agencies: {agenciesCount}<br/>" +
                              $"Total request types: {requestTypesCount}<br/>" +
                              $"Connection state: {connectionState}");
            }
            catch (Exception ex)
            {
                return Content($"Database Error: {ex.Message}<br/>" +
                              $"Stack Trace: {ex.StackTrace}");
            }
        }

        // اختبار الصلاحيات
        public ActionResult TestPermissions(Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                var hasPermission = ic.HasPer(UserID.ToString(), 100);
                return Content("User " + UserID + " has permission 100: " + hasPermission);
            }
            catch (Exception ex)
            {
                return Content("Permissions Error: " + ex.Message);
            }
        }

        // عرض جميع الطلبات
        public ActionResult a72001(int page = 1, int pageSize = 10, Guid UserID = default(Guid))
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var query = db.UnifiedRequests
                    .Include(r => r.RequestTypes)
                    .Include(r => r.Agency)
                    .Include(r => r.AgUsers)
                    .Include(r => r.Systems)
                    .Include(r => r.Users)
                    .Include(r => r.Users1)
                    .Where(r => r.Status > 0);
                
                // حساب العدد الإجمالي قبل الـ pagination
                var totalItems = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

                // تطبيق الـ pagination
                var obj = query
                    .OrderByDescending(r => r.RequestDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(r => new
                    {
                        r.RequestID,
                        RequestNumber = "REQ-" + r.RequestSeqNumber.ToString(),
                        RequestType = r.RequestTypes.TypeName,
                        AgencyName = r.Agency.AgencyName,
                         r.RequestDate,
                        r.Status,
                        SysName = r.Systems.SysName,
                        r.RequestedQuantity,
                        r.SerialNumber,
                        r.Notes,
                        RequestedBy = r.AgUsers.AgUserName,
                        StatusDate = r.UpdatedDate != null ? r.UpdatedDate : r.RequestDate,
                        ReviewedBy = r.ReviewedBy.HasValue ? r.Users.UserName : "",
                        ExecutedBy = r.ExecutedBy.HasValue ? r.Users1.UserName : ""
                    })
                    .ToList();
                var requests = obj.Select(r => new
                {
                    r.RequestID,
                    r.RequestNumber,
                    r.RequestType,
                    r.AgencyName,
                    r.SysName,
                    RequestDate = r.RequestDate.ToString("yyyy-MM-dd HH:mm tt"),
                    r.Status,
                    r.RequestedQuantity,
                    r.SerialNumber,
                    r.Notes,
                    r.RequestedBy,
                    StatusDate = r.StatusDate.ToString("yyyy-MM-dd HH:mm tt"),
                    r.ReviewedBy,
                    r.ExecutedBy
                }).ToList();
                return Json(new { 
                    ErrorCode = 0, 
                    RequestsList = requests,
                    TotalItems = totalItems,
                    TotalPages = totalPages,
                    CurrentPage = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء جلب البيانات: " + ex.Message });
            }
        }

        // عرض الطلبات الجديدة فقط
        public ActionResult a72002(Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var newRequests = db.UnifiedRequests
                    .Where(r => r.Status == 1) // طلبات جديدة
                    .OrderByDescending(r => r.RequestDate)
                    .Select(r => new
                    {
                        r.RequestID,
                        RequestNumber = "REQ-" + r.RequestSeqNumber.ToString(),
                        RequestType = r.RequestTypes.TypeName,
                        AgencyName = r.Agency.AgencyName,
                        SystemName = r.Systems != null ? r.Systems.SysName : "",
                        RequestDate =r.RequestDate.ToString("yyyy-MM-dd HH:mm tt"),
                        r.Status,
                        r.RequestedQuantity,
                        r.SerialNumber,
                        r.Notes,
                        RequestedBy = r.AgUsers.AgUserName,
                        StatusDate = r.RequestDate
                    }) .ToList();

                return Json(new { ErrorCode = 0, NewRequestsList = newRequests });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء جلب البيانات" });
            }
        }

        // عرض تفاصيل طلب معين
        public ActionResult a72003(Guid RequestID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }
           
                var request = db.UnifiedRequests
                    .Include(r => r.RequestTypes)
                    .Include(r => r.Agency)
                    .Include(r => r.AgUsers)
                    .Include(r => r.Systems)
                    .Include(r => r.Users)
                    .Include(r => r.Users1)
                    .Where(r => r.RequestID == RequestID).ToList();
               
             var RequestDetails = request.Select(r => new
                  {
                      r.RequestID,
                      RequestNumber = "REQ-" + r.RequestSeqNumber.ToString(),
                      RequestType = r.RequestTypes.TypeName,
                      r.RequestTypeID,
                      r.Agency.AgencyName,
                      r.AgencyID,
                      SystemName = r.Systems != null ? r.Systems.SysName : "",
                      r.SysTypeID,
                      RequestDate = r.RequestDate.ToString("yyyy-MM-dd HH:mm tt"),
                      r.Status,
                      r.RequestedQuantity,
                      r.SerialNumber,
                      r.CurrentStatusID,
                      r.RequestedStatusID,
                      r.ChangeReason,
                      r.Notes,
                      RequestedBy = r.AgUsers.AgUserName,
                      StatusDate = r.RequestDate.ToString("yyyy-MM-dd HH:mm tt"),
                      ReviewNotes = r.ReviewNotes,
                      ReviewDate = r.ReviewDate.HasValue ? r.ReviewDate.Value.ToString("yyyy-MM-dd HH:mm tt") : "",
                      ReviewedBy = r.ReviewedBy.HasValue ? (r.Users != null ? r.Users.UserName : "مستخدم غير معروف") : "",
                      ReviewedByID = r.ReviewedBy,
                      ExecutionNotes = r.ExecutionNotes,
                      ExecutedDate = r.ExecutedDate.HasValue ? r.ExecutedDate.Value.ToString("yyyy-MM-dd HH:mm tt") : "",
                      ExecutedBy = r.Users1 != null ? r.Users1.UserName : "",
                      ExecutedByID = r.ExecutedBy,
                        ReceiptNotes = r.ReceivedNotes,
                        ReceivedDate = r.ReceivedDate.HasValue ? r.ReceivedDate.Value.ToString("yyyy-MM-dd HH:mm tt") : "",
                      ReceivedBy = r.AgUsers.AgUserName,
                      r.RejectionReason,
                      r.StartSequence,
                      r.EndSequence,
                      r.Quantity,
                      InventDocsAvilable = r.SysTypeID == Guid.Parse("12196B48-95C5-4D51-A13D-091FD6D73E37") ? db.SerialNums.Where(c=>c.AgentID == r.AgencyID && c.Status == 1).Count() :
                      r.SysTypeID == Guid.Parse("7F8703F3-60AC-47C6-828D-29F8EA1B6246") ? db.Oran_SerialNums.Where(c => c.AgencyID == r.AgencyID && c.Status == 1).Count() :
                      r.SysTypeID == Guid.Parse("F43D1CDD-BC69-42C9-9E73-D8249DDD4ADE") ? db.Trav_SerialNums.Where(c => c.AgentID == r.AgencyID && c.Status == 1).Count() :
                      r.SysTypeID == Guid.Parse("85A70D0B-C1CC-4B8E-8191-DBFB6ADE02A8") ? db.MidcRes_SerialNums.Where(c => c.AgentID == r.AgencyID && c.Status == 1).Count() :0
                      })   .FirstOrDefault();

                if (RequestDetails == null)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لم يتم العثور على الطلب" });
                }

                return Json(new { ErrorCode = 0, RequestDetails });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء جلب تفاصيل الطلب" });
            }
        }

        // مراجعة طلب (الموافقة أو الرفض)
        public ActionResult a72004(Guid RequestID, bool IsApproved, string ReviewNotes, string RejectionReason, 
            int? StartSequence, int? Quantity, int? EndSequence, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var request = db.UnifiedRequests.FirstOrDefault(r => r.RequestID == RequestID);
                if (request == null)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لم يتم العثور على الطلب" });
                }

                if (request.Status != 1)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لا يمكن مراجعة هذا الطلب في حالته الحالية" });
                }

                // تحديث حالة الطلب
                request.Status = (byte)(IsApproved ? 3 : 4); // 3=معتمد، 4=مرفوض
                request.ReviewedBy = UserID;
                request.ReviewDate = DateTime.Now;
                request.UpdatedDate = DateTime.Now;
                request.UpdatedBy = UserID;

                if (!IsApproved)
                {
                    request.RejectionReason = RejectionReason;
                }
                else
                {
                    // حفظ بيانات التسلسل إذا تمت الموافقة
                    if (StartSequence.HasValue && Quantity.HasValue && EndSequence.HasValue)
                    {
                        request.StartSequence = StartSequence.Value;
                        request.Quantity = Quantity.Value;
                        request.EndSequence = EndSequence.Value;
                    }
                }

                db.SaveChanges();

                return Json(new { ErrorCode = 0, Message = IsApproved ? "تم اعتماد الطلب بنجاح" : "تم رفض الطلب" });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء مراجعة الطلب" });
            }
        }

        // تنفيذ طلب معتمد
        public ActionResult a72005(Guid RequestID, string ExecutionNotes, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var request = db.UnifiedRequests.FirstOrDefault(r => r.RequestID == RequestID);
                if (request == null)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لم يتم العثور على الطلب" });
                }

                if (request.Status != 3)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لا يمكن تنفيذ هذا الطلب في حالته الحالية" });
                }

                // تحديث حالة الطلب إلى منفذ
                request.Status = 5; // منفذ
                request.ExecutedBy = UserID;
                request.ExecutedDate = DateTime.Now;
                request.UpdatedDate = DateTime.Now;
                request.UpdatedBy = UserID;

                db.SaveChanges();

                return Json(new { ErrorCode = 0, Message = "تم تنفيذ الطلب بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء تنفيذ الطلب" });
            }
        }

        // إحصائيات الطلبات
        public ActionResult a72006(Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var stats = new
                {
                    TotalRequests = db.UnifiedRequests.Count(),
                    NewRequests = db.UnifiedRequests.Count(r => r.Status == 1),
                    UnderReview = db.UnifiedRequests.Count(r => r.Status == 2),
                    Approved = db.UnifiedRequests.Count(r => r.Status == 3),
                    Rejected = db.UnifiedRequests.Count(r => r.Status == 4),
                    Executed = db.UnifiedRequests.Count(r => r.Status == 5),
                    Received = db.UnifiedRequests.Count(r => r.Status == 6),
                    TodayRequests = db.UnifiedRequests.Count(r =>DbFunctions.TruncateTime( r.RequestDate) == DbFunctions.TruncateTime(DateTime.Now))
                };

                return Json(new { ErrorCode = 0, Statistics = stats });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء جلب الإحصائيات" });
            }
        }

        // البحث في الطلبات مع الـ pagination
        public ActionResult a72007(string SearchTerm, byte? StatusFilter, DateTime? DateFrom, DateTime? DateTo, 
                                  int page = 1, int pageSize = 10, Guid UserID = default(Guid))
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var query = db.UnifiedRequests
                    .Include(r => r.RequestTypes)
                    .Include(r => r.Agency)
                    .Include(r => r.AgUsers)
                    .Include(r => r.Systems)
                    .Include(r => r.Users)
                    .Include(r => r.Users1)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (!string.IsNullOrEmpty(SearchTerm))
                {
                    query = query.Where(r => r.Agency.AgencyName.Contains(SearchTerm) ||
                                           r.SerialNumber.Contains(SearchTerm) ||
                                           ("REQ-" + r.RequestSeqNumber.ToString()).Contains(SearchTerm));
                }

                if (StatusFilter.HasValue)
                {
                    query = query.Where(r => r.Status == StatusFilter.Value);
                }

                if (DateFrom.HasValue)
                {
                    query = query.Where(r => r.RequestDate >= DateFrom.Value);
                }

                if (DateTo.HasValue)
                {
                    query = query.Where(r => r.RequestDate <= DateTo.Value);
                }

                // حساب العدد الإجمالي قبل الـ pagination
                var totalItems = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

                // تطبيق الـ pagination
                var results = query
                    .OrderByDescending(r => r.RequestDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(r => new
                    {
                        r.RequestID,
                        RequestNumber = "REQ-" + r.RequestSeqNumber.ToString(),
                        RequestType = r.RequestTypes.TypeName,
                        AgencyName = r.Agency.AgencyName,
                        SysName = r.Systems != null ? r.Systems.SysName : "",
                        RequestDate =  r.RequestDate.ToString("yyyy-MM-dd HH:mm tt"),
                        r.Status,
                        r.RequestedQuantity,
                        r.SerialNumber,
                        r.Notes,
                        RequestedBy = r.AgUsers.AgUserName,
                        StatusDate = r.UpdatedDate.HasValue ? r.UpdatedDate.Value.ToString("yyyy-MM-dd HH:mm tt") : r.RequestDate.ToString("yyyy-MM-dd HH:mm tt"),
                        ReviewedBy = r.ReviewedBy.HasValue ? r.Users.UserName : "",
                        ExecutedBy = r.ExecutedBy.HasValue ? r.Users1.UserName : ""
                    })
                    .ToList();

                return Json(new { 
                    ErrorCode = 0, 
                    SearchResults = results,
                    TotalItems = totalItems,
                    TotalPages = totalPages,
                    CurrentPage = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء البحث: " + ex.Message });
            }
        }

        // حفظ بيانات المراجعة في قاعدة البيانات
        public ActionResult a72008(Guid RequestID, bool IsApproved, string ReviewNotes, string RejectionReason, 
            int? StartSequence, int? Quantity, int? EndSequence, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var request = db.UnifiedRequests.FirstOrDefault(r => r.RequestID == RequestID);
                if (request == null)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لم يتم العثور على الطلب" });
                }

                // حفظ بيانات المراجعة
                request.ReviewNotes = ReviewNotes;
                request.RejectionReason = RejectionReason;
                request.ReviewedBy = UserID;
                request.ReviewDate = DateTime.Now;
                request.UpdatedDate = DateTime.Now;
                request.UpdatedBy = UserID;

                // حفظ بيانات التسلسل إذا تمت الموافقة
                if (IsApproved && StartSequence.HasValue && Quantity.HasValue && EndSequence.HasValue)
                {
                    request.StartSequence = StartSequence.Value;
                    request.Quantity = Quantity.Value;
                    request.EndSequence = EndSequence.Value;
                }

                db.SaveChanges();

                return Json(new { ErrorCode = 0, Message = "تم حفظ بيانات المراجعة بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء حفظ بيانات المراجعة: " + ex.Message });
            }
        }

        // تحديث وضع الطلب إلى "قيد المراجعة"
        public ActionResult a72009(Guid RequestID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 100))
                {
                    return Json(new { ErrorCode = 3, ErrorMessage = "لا تملك الصلاحيات المناسبة" });
                }

                var request = db.UnifiedRequests.FirstOrDefault(r => r.RequestID == RequestID);
                if (request == null)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لم يتم العثور على الطلب" });
                }

                if (request.Status != 1)
                {
                    return Json(new { ErrorCode = 2, ErrorMessage = "لا يمكن تحديث هذا الطلب في حالته الحالية" });
                }

                // تحديث حالة الطلب إلى "قيد المراجعة"
                request.Status = 2;
                request.UpdatedDate = DateTime.Now;
                request.UpdatedBy = UserID;

                db.SaveChanges();

                return Json(new { ErrorCode = 0, Message = "تم تحديث حالة الطلب إلى قيد المراجعة" });
            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "حدث خطأ أثناء تحديث الطلب" });
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
} 