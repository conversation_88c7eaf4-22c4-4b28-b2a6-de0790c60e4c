<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <!-- Versioning Configuration -->
    <add key="AutoVersioning:Enabled" value="true" />
    <add key="AutoVersioning:UseFileVersion" value="false" />
    <add key="AutoVersioning:UseTimestamp" value="true" />
    <add key="AutoVersioning:CacheBusting" value="true" />
    <!-- Session Management Configuration -->
    <add key="JWT_SECRET_KEY" value="YourSuperSecretKeyForJWTTokenGeneration2024!@#$%^&amp;*()" />
    <add key="JWT_ISSUER" value="Inus_AppsMang" />
    <add key="JWT_AUDIENCE" value="Inus_AppsMang_Users" />
    <add key="SESSION_DURATION_HOURS" value="24" />
    <add key="SESSION_REFRESH_THRESHOLD_MINUTES" value="30" />
  </appSettings>
  <system.web>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.8" requestValidationMode="2.0" />
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" culture="ar-LY" uiCulture="ar-LY" />
    
    <customErrors mode="On" defaultRedirect="~/Error">
        <error statusCode="404" redirect="~/Error" />
        <error statusCode="500" redirect="~/Error" />
    </customErrors>
    
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="HTTP to HTTPS redirect" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true" />
          </conditions>
          <action type="Redirect" redirectType="Permanent" url="https://{HTTP_HOST}/{R:1}" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
  <connectionStrings>
	  <!--<add name="Insh_AppsDBEntities" connectionString="metadata=res://*/Insh_AppMang.csdl|res://*/Insh_AppMang.ssdl|res://*/Insh_AppMang.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=DESKTOP-CPAFRGF\SQLEXPRESS;initial catalog=Test_Insh_AppsDB;persist security info=True;user id=Ins_app;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="Insh_AppsDBEntities" connectionString="metadata=res://*/Insh_AppMang.csdl|res://*/Insh_AppMang.ssdl|res://*/Insh_AppMang.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=*************,2025;initial catalog=Insh_AppsDB;persist security info=True;user id=app_user;password=App_user@123;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="Insh_AppsDBEntities" connectionString="metadata=res://*/Insh_AppMang.csdl|res://*/Insh_AppMang.ssdl|res://*/Insh_AppMang.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=*************,1433;initial catalog=Insh_AppsDB;user id=sp_admin;password=************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <add name="Insh_AppsDBEntities" connectionString="metadata=res://*/Insh_AppMang.csdl|res://*/Insh_AppMang.ssdl|res://*/Insh_AppMang.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***************,1433;initial catalog=Test_Insh_AppsDB;user id=Ins_app;password=InsUser@123;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <!--<add name="Insh_AppsDBEntities" connectionString="metadata=res://*/Insh_AppMang.csdl|res://*/Insh_AppMang.ssdl|res://*/Insh_AppMang.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=**************,1433;initial catalog=Test_Insh_AppsDB;user id=Ins_app;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>