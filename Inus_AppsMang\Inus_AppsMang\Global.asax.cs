﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;

namespace Inus_AppsMang
{
    public class MvcApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            
            // إعداد Entity Framework
            System.Data.Entity.Database.SetInitializer<Insh_AppsDBEntities>(null);
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            Exception ex = Server.GetLastError();
            
            // تسجيل الخطأ
            System.Diagnostics.Debug.WriteLine("Global Error: " + ex.Message);
            
            // إعادة توجيه لصفحة الخطأ
            Server.ClearError();
            Response.Redirect("~/Error");
        }
    }
}
