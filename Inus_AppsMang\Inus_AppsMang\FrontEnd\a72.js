(function () {
    'use strict';

    var controllerId = 'a72';
    angular.module('App').controller(controllerId, ["DataService", '$filter', "Notification", "blockUI", "$stateParams", "$state", Func]);

    function Func(DataService, $filter, Notification, blockUI, $stateParams, $state) {
        var vm = this;

        // تهيئة الجلسة
        vm.initializeSession = function() {
            var sessionToken = localStorage.getItem('sessionToken');
            var userID = localStorage.getItem('userID');
            var userName = localStorage.getItem('userName');
          
            if (!sessionToken || !userID) {
                $state.go('LoginPage');
                return false;
            }
            
            // التحقق من صحة الجلسة
            DataService.a29002(sessionToken).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    vm.UserID = data.UserID;
                    vm.UserName = data.UserName;
                    vm.SessionToken = sessionToken;
                    vm.loadInitialData();
                    
                } else {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                }
            }, function() {
                vm.clearSessionData();
                $state.go('LoginPage');
            });
        };

        // مسح بيانات الجلسة
        vm.clearSessionData = function() {
            localStorage.removeItem('sessionToken');
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('userID');
            localStorage.removeItem('userName');
            localStorage.removeItem('compID');
        };

        // تسجيل الخروج
        vm.logout = function() {
            var sessionToken = localStorage.getItem('sessionToken');
            if (sessionToken) {
                DataService.a29003(sessionToken).then(function() {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    Notification.success({ 
                        message: "تم تسجيل الخروج بنجاح", 
                        title: 'تسجيل الخروج' 
                    });
                }, function() {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                });
            } else {
                vm.clearSessionData();
                $state.go('LoginPage');
            }
        };

        // تهيئة المتغيرات
        vm.requestsList = []; // البيانات المعروضة من الخادم
        vm.statistics = {};
        vm.searchTerm = '';
        vm.statusFilter = '';
        vm.dateFrom = '';
        vm.dateTo = '';
        vm.selectedRequest = {};
        vm.reviewData = {
            RequestID: '',
            RequestNumber: '',
            IsApproved: true,
            ReviewNotes: '',
            RejectionReason: '',
            StartSequence: '',
            Quantity: '',
            EndSequence: ''
        };
        vm.executionData = {
            RequestID: '',
            ExecutionNotes: ''
        };
        
        // متغيرات الطباعة
        vm.currentDate = new Date();

        // متغيرات الـ pagination (يتم تعيينها من الخادم)
        vm.currentPage = 1;
        vm.totalPages = 1;
        vm.pageSize = 10;
        vm.totalItems = 0;

        vm.loadInitialData = function() {
            vm.a72001(); 
            vm.a72006(); 
        };

        // تحميل جميع الطلبات مع الـ pagination
        vm.a72001 = function() {
            blockUI.start();
            DataService.a72001(vm.currentPage, vm.pageSize, vm.UserID).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    vm.requestsList = data.RequestsList;
                    vm.totalItems = data.TotalItems;
                    vm.totalPages = data.TotalPages;
                    vm.currentPage = data.CurrentPage;
                    vm.pageSize = data.PageSize;
                    blockUI.stop();
                } else if (data.ErrorCode === 3) {
                    Notification.error({ 
                        message: "لا تملك الصلاحيات المناسبة", 
                        title: 'خطأ في الصلاحيات' 
                    });
                    blockUI.stop();
                } else {
                    Notification.error({ 
                        message: data.ErrorMessage || "حدث خطأ أثناء تحميل البيانات", 
                        title: 'خطأ' 
                    });
                    blockUI.stop();
                }
            }, function() {
                Notification.error({ 
                    message: "لا يمكن الوصول إلى الخادم", 
                    title: 'خطأ في الاتصال' 
                });
                blockUI.stop();
            });
        };

        // تحميل الإحصائيات
        vm.a72006 = function() {
            DataService.a72006(vm.UserID).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    vm.statistics = data.Statistics;
                }
            });
        };

        // عرض تفاصيل الطلب
        vm.viewRequestDetails = function(request) {
            blockUI.start();
            DataService.a72003(request.RequestID, vm.UserID).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    vm.selectedRequest = data.RequestDetails;

                    // طباعة البيانات للتشخيص
                    console.log('Request Details:', vm.selectedRequest);
                    console.log('Status:', vm.selectedRequest.Status);
                    console.log('StartSequence:', vm.selectedRequest.StartSequence);
                    console.log('EndSequence:', vm.selectedRequest.EndSequence);
                    console.log('Quantity:', vm.selectedRequest.Quantity);
                    console.log('ReviewedBy:', vm.selectedRequest.ReviewedBy);
                    console.log('ReviewedByID:', vm.selectedRequest.ReviewedByID);
                    console.log('ReviewDate:', vm.selectedRequest.ReviewDate);
                    console.log('ReviewNotes:', vm.selectedRequest.ReviewNotes);
                    console.log('ExecutedBy:', vm.selectedRequest.ExecutedBy);
                    console.log('ExecutedByID:', vm.selectedRequest.ExecutedByID);
                    console.log('ExecutedDate:', vm.selectedRequest.ExecutedDate);
                    console.log('ExecutionNotes:', vm.selectedRequest.ExecutionNotes);

                    // تشخيص إضافي
                    if (vm.selectedRequest.Status === 3) {
                        console.log('هذا طلب معتمد - يجب أن تظهر معلومات المراجع');
                        if (!vm.selectedRequest.ReviewedBy) {
                            console.warn('تحذير: الطلب معتمد لكن لا توجد معلومات مراجع!');
                        }
                    }
                    if (vm.selectedRequest.Status === 5) {
                        console.log('هذا طلب منفذ - يجب أن تظهر معلومات التنفيذ');
                        console.log('ExecutedBy:', vm.selectedRequest.ExecutedBy);
                        console.log('ExecutedDate:', vm.selectedRequest.ExecutedDate);
                        console.log('ExecutionNotes:', vm.selectedRequest.ExecutionNotes);
                        if (!vm.selectedRequest.ExecutedBy) {
                            console.warn('تحذير: الطلب منفذ لكن لا توجد معلومات تنفيذ!');
                        }
                    }

                    $('#RequestDetailsModal').modal('show');
                } else {
                    Notification.error({
                        message: data.ErrorMessage || "حدث خطأ أثناء تحميل تفاصيل الطلب",
                        title: 'خطأ'
                    });
                }
                blockUI.stop();
            }, function() {
                Notification.error({
                    message: "لا يمكن الوصول إلى الخادم",
                    title: 'خطأ في الاتصال'
                });
                blockUI.stop();
            });
        };

        // حساب نهاية التسلسل
        vm.calculateEndSequence = function() {
            if (vm.reviewData.StartSequence && vm.reviewData.Quantity) {
                var start = parseInt(vm.reviewData.StartSequence);
                var quantity = parseInt(vm.reviewData.Quantity);
                if (!isNaN(start) && !isNaN(quantity) && quantity > 0) {
                    vm.reviewData.EndSequence = start + quantity - 1;
                } else {
                    vm.reviewData.EndSequence = '';
                }
            } else {
                vm.reviewData.EndSequence = '';
            }
        };

        // بدء مراجعة الطلب
        vm.startReview = function(request) {
            vm.reviewData.RequestID = request.RequestID;
            vm.reviewData.RequestNumber = request.RequestNumber;
            vm.selectedRequest = request;
            vm.reviewData.ReviewNotes = '';
            vm.reviewData.RejectionReason = '';
            vm.reviewData.IsApproved = true;
            vm.reviewData.StartSequence = '';
            vm.reviewData.Quantity = '';
            vm.reviewData.EndSequence = '';
            $('#ReviewRequestModal').modal('show');
        };

        // عرض تقرير المراجعة للطباعة - تحسين الدالة
        vm.printReviewReport = function() {
            // التحقق من البيانات المطلوبة
            if (!vm.reviewData.IsApproved) {
                Notification.error({ 
                    message: "يرجى اختيار قرار المراجعة أولاً", 
                    title: 'خطأ في البيانات' 
                });
                return;
            }
            
            if (!vm.reviewData.ReviewNotes || vm.reviewData.ReviewNotes.trim() === '') {
                Notification.error({ 
                    message: "يرجى إدخال ملاحظات المراجعة أولاً", 
                    title: 'خطأ في البيانات' 
                });
                return;
            }
            
            // تعيين التاريخ الحالي
            vm.currentDate = new Date();
            
            // تحضير البيانات للتقرير
            vm.prepareReportData();
            
            // فتح مودال التقرير
            $('#reviewReportModal').modal('show');
            
            // إظهار رسالة للمستخدم
            Notification.success({ 
                message: "تم تحضير التقرير للطباعة. يمكنك الآن طباعة التقرير", 
                title: 'تقرير جاهز' 
            });
        };

        // طباعة التقرير - إصلاح دالة الطباعة
        vm.printReport = function() {
            // تأكد من أن البيانات متوفرة
            if (!vm.reviewData || !vm.selectedRequest) {
                Notification.error({ 
                    message: "لا توجد بيانات للطباعة", 
                    title: 'خطأ في الطباعة' 
                });
                return;
            }
            
            try {
                // إغلاق المودال أولاً
                $('#reviewReportModal').modal('hide');
                
                // انتظار قليل ثم الطباعة
                setTimeout(function() {
                    // الحصول على محتوى التقرير
                    var reportContent = document.getElementById('reviewReportContent');
                    if (!reportContent) {
                        console.error('لم يتم العثور على محتوى التقرير');
                        return;
                    }
                    
                    // إنشاء نافذة طباعة جديدة
                    var printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                    
                    // إضافة أنماط الطباعة
                    var printStyles = `
                        <style>
                            body { 
                                font-family: 'Cairo', Arial, sans-serif; 
                                direction: rtl; 
                                text-align: right; 
                                margin: 20px; 
                                padding: 0; 
                                background: white; 
                                color: black; 
                                font-size: 14px;
                                line-height: 1.6;
                            }
                            .row { 
                                display: block; 
                                margin-bottom: 15px; 
                                clear: both;
                            }
                            .col-md-6, .col-md-4, .col-12 { 
                                display: block; 
                                width: 100%; 
                                margin-bottom: 8px; 
                                float: right;
                            }
                            .badge { 
                                border: 1px solid #000; 
                                color: #000; 
                                background: transparent; 
                                padding: 4px 8px; 
                                border-radius: 4px; 
                                font-size: 12px;
                            }
                            .bg-success { 
                                background-color: #d4edda !important; 
                                color: #155724 !important; 
                                border-color: #155724 !important;
                            }
                            .bg-danger { 
                                background-color: #f8d7da !important; 
                                color: #721c24 !important; 
                                border-color: #721c24 !important;
                            }
                            .bg-warning { 
                                background-color: #fff3cd !important; 
                                color: #856404 !important; 
                                border-color: #856404 !important;
                            }
                            .bg-light { 
                                background-color: #f8f9fa !important; 
                                color: #000 !important; 
                                border: 1px solid #dee2e6 !important;
                            }
                            h3, h4, h5, h6 { 
                                color: #000 !important; 
                                font-weight: bold !important; 
                                margin-bottom: 10px;
                            }
                            p, span, div { 
                                color: #000 !important; 
                            }
                            .border { 
                                border: 1px solid #ccc !important; 
                            }
                            .rounded { 
                                border-radius: 4px !important; 
                            }
                            .p-3 { 
                                padding: 15px !important; 
                            }
                            .mt-2 { 
                                margin-top: 10px !important; 
                            }
                            .mb-3 { 
                                margin-bottom: 15px !important; 
                            }
                            .mb-4 { 
                                margin-bottom: 20px !important; 
                            }
                            .my-3 { 
                                margin: 15px 0 !important; 
                            }
                            .text-center { 
                                text-align: center !important; 
                            }
                            .text-muted { 
                                color: #6c757d !important; 
                            }
                            .fw-bold { 
                                font-weight: bold !important; 
                            }
                            .ms-2 { 
                                margin-right: 8px !important; 
                            }
                            hr { 
                                border: 1px solid #ccc !important; 
                                margin: 20px 0;
                            }
                            small { 
                                font-size: 12px !important; 
                            }
                            strong {
                                font-weight: bold !important;
                            }
                            @media print {
                                body { 
                                    margin: 0; 
                                    padding: 20px; 
                                    font-size: 12pt;
                                }
                                .row { 
                                    page-break-inside: avoid; 
                                }
                            }
                        </style>
                    `;
                    
                    // إنشاء محتوى الصفحة الكامل
                    var fullContent = `
                        <!DOCTYPE html>
                        <html dir="rtl" lang="ar">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>تقرير مراجعة الطلب - ${vm.reviewData.RequestNumber}</title>
                            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
                            ${printStyles}
                        </head>
                        <body>
                            ${reportContent.innerHTML}
                            <script>
                                // طباعة تلقائية عند تحميل الصفحة
                                window.onload = function() {
                                    setTimeout(function() {
                                        window.print();
                                    }, 1000);
                                };
                                
                                // إغلاق النافذة بعد الطباعة
                                window.onafterprint = function() {
                                    setTimeout(function() {
                                        window.close();
                                    }, 1000);
                                };
                                
                                // طباعة يدوية عند الضغط على Ctrl+P
                                document.addEventListener('keydown', function(e) {
                                    if (e.ctrlKey && e.key === 'p') {
                                        e.preventDefault();
                                        window.print();
                                    }
                                });
                            </script>
                        </body>
                        </html>
                    `;
                    
                    // كتابة المحتوى في النافذة الجديدة
                    printWindow.document.write(fullContent);
                    printWindow.document.close();
                    
                    // إظهار رسالة للمستخدم
                    Notification.success({ 
                        message: "تم فتح نافذة الطباعة. سيتم الطباعة تلقائياً أو اضغط Ctrl+P للطباعة اليدوية", 
                        title: 'طباعة التقرير' 
                    });
                    
                }, 500);
                
            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                Notification.error({ 
                    message: "حدث خطأ أثناء الطباعة: " + error.message, 
                    title: 'خطأ في الطباعة' 
                });
                
                // إعادة فتح المودال في حالة الخطأ
                setTimeout(function() {
                    $('#reviewReportModal').modal('show');
                }, 1000);
            }
        };

        // دالة طباعة بديلة - طباعة مباشرة
        vm.printReportDirect = function() {
            // تأكد من أن البيانات متوفرة
            if (!vm.reviewData || !vm.selectedRequest) {
                Notification.error({ 
                    message: "لا توجد بيانات للطباعة", 
                    title: 'خطأ في الطباعة' 
                });
                return;
            }
            
            try {
                // إغلاق المودال أولاً
                $('#reviewReportModal').modal('hide');
                
                // انتظار قليل ثم الطباعة
                setTimeout(function() {
                    // طباعة مباشرة للصفحة الحالية
                    window.print();
                    
                    // إعادة فتح المودال بعد الطباعة
                    setTimeout(function() {
                        $('#reviewReportModal').modal('show');
                    }, 2000);
                    
                }, 500);
                
            } catch (error) {
                console.error('خطأ في الطباعة المباشرة:', error);
                Notification.error({ 
                    message: "حدث خطأ أثناء الطباعة: " + error.message, 
                    title: 'خطأ في الطباعة' 
                });
                
                // إعادة فتح المودال في حالة الخطأ
                setTimeout(function() {
                    $('#reviewReportModal').modal('show');
                }, 1000);
            }
        };

        // حفظ بيانات المراجعة في قاعدة البيانات
        vm.saveReviewData = function() {
            if (!vm.reviewData.RequestID) {
                Notification.error({ 
                    message: "لا يوجد طلب محدد للحفظ", 
                    title: 'خطأ في البيانات' 
                });
                return;
            }
            
            blockUI.start();
            
            DataService.a72008(
                vm.reviewData.RequestID,
                vm.reviewData.IsApproved === 'true',
                vm.reviewData.ReviewNotes,
                vm.reviewData.RejectionReason,
                vm.reviewData.StartSequence,
                vm.reviewData.Quantity,
                vm.reviewData.EndSequence,
                vm.UserID
            ).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    Notification.success({ 
                        message: data.Message || "تم حفظ بيانات المراجعة بنجاح", 
                        title: 'نجح الحفظ' 
                    });
                    
                    // إغلاق المودال
                    $('#reviewReportModal').modal('hide');
                    $('#reviewRequestModal').modal('hide');
                    
                    // إعادة تحميل البيانات
                    vm.a72001();
                    vm.a72006();
                } else {
                    Notification.error({ 
                        message: data.ErrorMessage || "حدث خطأ أثناء حفظ البيانات", 
                        title: 'خطأ في الحفظ' 
                    });
                }
                blockUI.stop();
            }, function() {
                Notification.error({ 
                    message: "لا يمكن الوصول إلى الخادم", 
                    title: 'خطأ في الاتصال' 
                });
                blockUI.stop();
            });
        };

        // مراجعة الطلب (موافقة/رفض)
        vm.a72004 = function(isApproved) {
            vm.reviewData.IsApproved = isApproved;
            
            if (!isApproved && !vm.reviewData.RejectionReason) {
                Notification.error({ 
                    message: "يرجى إدخال سبب الرفض", 
                    title: 'خطأ في البيانات' 
                });
                return;
            }

            blockUI.start();
            DataService.a72004(
                vm.reviewData.RequestID,
                vm.reviewData.IsApproved,
                vm.reviewData.ReviewNotes,
                vm.reviewData.RejectionReason,
                vm.reviewData.StartSequence,
                vm.reviewData.Quantity,
                vm.reviewData.EndSequence,
                vm.UserID
            ).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    Notification.success({ 
                        message: data.Message, 
                        title: 'تم بنجاح' 
                    });
                    
                    // إغلاق مودال المراجعة
                    $('#ReviewRequestModal').modal('hide');
                    
                    // إعادة تحميل البيانات
                    vm.a72001(); // إعادة تحميل الطلبات
                    vm.a72006(); // تحديث الإحصائيات
                    
                    // إذا كانت الموافقة، اعرض نافذة الطباعة
                    if (isApproved) {
                        // تعيين التاريخ الحالي
                        vm.currentDate = new Date();
                        
                        // تحضير البيانات للتقرير
                        vm.prepareReportData();
                        
                        // انتظار قليل ثم فتح مودال التقرير
                        setTimeout(function() {
                            $('#reviewReportModal').modal('show');
                            
                            // إظهار رسالة للمستخدم
                            Notification.success({ 
                                message: "تم تأكيد المراجعة بنجاح. يمكنك الآن طباعة التقرير", 
                                title: 'تقرير جاهز للطباعة' 
                            });
                        }, 500);
                    }
                    
                } else {
                    Notification.error({ 
                        message: data.ErrorMessage || "حدث خطأ أثناء مراجعة الطلب", 
                        title: 'خطأ' 
                    });
                }
                blockUI.stop();
            }, function() {
                Notification.error({ 
                    message: "لا يمكن الوصول إلى الخادم", 
                    title: 'خطأ في الاتصال' 
                });
                blockUI.stop();
            });
        };

        // تنفيذ الطلب
        vm.executeRequest = function(request) {
            vm.executionData.RequestID = request.RequestID;
            vm.executionData.RequestNumber = request.RequestNumber;
            vm.executionData.ExecutionNotes = '';
            $('#ExecuteRequestModal').modal('show');
        };

        // تأكيد تنفيذ الطلب
        vm.a72005 = function() {
            blockUI.start();
            DataService.a72005(
                vm.executionData.RequestID,
                vm.executionData.ExecutionNotes,
                vm.UserID
            ).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    Notification.success({ 
                        message: data.Message, 
                        title: 'تم بنجاح' 
                    });
                    $('#ExecuteRequestModal').modal('hide');
                    vm.a72001(); // إعادة تحميل الطلبات
                    vm.a72006(); // تحديث الإحصائيات
                } else {
                    Notification.error({ 
                        message: data.ErrorMessage || "حدث خطأ أثناء تنفيذ الطلب", 
                        title: 'خطأ' 
                    });
                }
                blockUI.stop();
            }, function() {
                Notification.error({ 
                    message: "لا يمكن الوصول إلى الخادم", 
                    title: 'خطأ في الاتصال' 
                });
                blockUI.stop();
            });
        };

        // البحث في الطلبات مع الـ pagination
        vm.searchRequests = function() {
            blockUI.start();
            DataService.a72007(
                vm.searchTerm,
                vm.statusFilter,
                vm.dateFrom,
                vm.dateTo,
                vm.currentPage,
                vm.pageSize,
                vm.UserID
            ).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    vm.requestsList = data.SearchResults;
                    vm.totalItems = data.TotalItems;
                    vm.totalPages = data.TotalPages;
                    vm.currentPage = data.CurrentPage;
                    vm.pageSize = data.PageSize;
                } else {
                    Notification.error({ 
                        message: data.ErrorMessage || "حدث خطأ أثناء البحث", 
                        title: 'خطأ' 
                    });
                }
                blockUI.stop();
            }, function() {
                Notification.error({ 
                    message: "لا يمكن الوصول إلى الخادم", 
                    title: 'خطأ في الاتصال' 
                });
                blockUI.stop();
            });
        };

        // مسح الفلاتر
        vm.clearFilters = function() {
            vm.searchTerm = '';
            vm.statusFilter = '';
            vm.dateFrom = '';
            vm.dateTo = '';
            vm.currentPage = 1; // إعادة تعيين الصفحة الحالية
            vm.a72001(); // إعادة تحميل جميع الطلبات
        };

        // إعادة تعيين الصفحة عند البحث
        vm.resetPageAndSearch = function() {
            vm.currentPage = 1;
            vm.searchRequests();
        };

        // الحصول على اسم الحالة
        vm.getStatusName = function(status) {
            var statusNames = {
                1: 'جديد',
                2: 'قيد المراجعة',
                3: 'معتمد',
                4: 'مرفوض',
                5: 'منفذ',
                6: 'مستلم'
            };
            return statusNames[status] || 'غير محدد';
        };

        // الحصول على كلاس الحالة
        vm.getStatusClass = function(status) {
            var statusClasses = {
                1: 'badge-secondary',
                2: 'badge-warning',
                3: 'badge-success',
                4: 'badge-danger',
                5: 'badge-info',
                6: 'badge-primary'
            };
            return statusClasses[status] || 'badge-light';
        };

        // الذهاب إلى صفحة الأنظمة
        vm.sys_page = function() {
            $state.go('SystemsPage');
        };

        // العودة للصفحة الرئيسية
        vm.goHome = function() {
            $state.go('HomePage');
        };

        // رجوع
        vm.Back = function() {
            $state.go('MangPlatformPage');
        };

        // تحديث البيانات
        vm.RefreshData = function() {
            vm.loadInitialData();
            Notification.success({ 
                message: "تم تحديث البيانات بنجاح", 
                title: 'تحديث البيانات' 
            });
        };

        // الصفحة السابقة
        vm.previousPage = function() {
            if (vm.currentPage > 1) {
                vm.currentPage--;
                vm.loadPageData();
            }
        };

        // الصفحة التالية
        vm.nextPage = function() {
            if (vm.currentPage < vm.totalPages) {
                vm.currentPage++;
                vm.loadPageData();
            }
        };

        // الانتقال إلى صفحة معينة
        vm.goToPage = function(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= vm.totalPages) {
                vm.currentPage = pageNumber;
                vm.loadPageData();
            }
        };

        // تحميل بيانات الصفحة الحالية
        vm.loadPageData = function() {
            // إذا كان هناك بحث نشط، استخدم البحث، وإلا استخدم تحميل البيانات العادي
            if (vm.searchTerm || vm.statusFilter || vm.dateFrom || vm.dateTo) {
                vm.searchRequests();
            } else {
                vm.a72001();
            }
        };

        // تهيئة الجلسة عند بدء التطبيق
        vm.initializeSession();

        // تحضير بيانات التقرير
        vm.prepareReportData = function() {
            // التأكد من وجود البيانات المطلوبة
            if (!vm.selectedRequest) {
                vm.selectedRequest = {
                    AgencyName: 'غير محدد',
                    SysName: 'غير محدد',
                    RequestDate: new Date(),
                    RequestedBy: 'غير محدد',
                    RequestType: 'غير محدد',
                    RequestedQuantity: 0
                };
            }
            
            // حساب نهاية التسلسل إذا لم تكن محسوبة
            if (vm.reviewData.StartSequence && vm.reviewData.Quantity && !vm.reviewData.EndSequence) {
                vm.calculateEndSequence();
            }
        };

        // دالة لتفعيل/تعطيل زر الطباعة
        vm.isPrintButtonEnabled = function() {
            return vm.reviewData.IsApproved && 
                   vm.reviewData.ReviewNotes && 
                   vm.reviewData.ReviewNotes.trim() !== '';
        };

        // دالة لتفعيل/تعطيل زر تأكيد المراجعة
        vm.isConfirmButtonEnabled = function() {
            return vm.reviewData.IsApproved;
        };

        // دالة طباعة تقرير الطلب المعتمد
        vm.printApprovedRequestReport = function() {
            // التحقق من أن الطلب معتمد
            if (!vm.selectedRequest || vm.selectedRequest.Status !== 3) {
                Notification.error({
                    message: "يمكن طباعة المراجعة للطلبات المعتمدة فقط",
                    title: 'خطأ في الطباعة'
                });
                return;
            }

            // تعيين التاريخ الحالي
            vm.currentDate = new Date();

            // تحديث البيانات للتأكد من أنها محدثة
            vm.updatePrintData();

            // فتح مودال التقرير مع تأخير بسيط للسماح لـ Angular بتحديث البيانات
            setTimeout(function() {
                $('#approvedRequestReportModal').modal('show');
            }, 100);

            // إظهار رسالة للمستخدم
            Notification.success({
                message: "تم تحضير تقرير الطلب المعتمد للطباعة",
                title: 'تقرير جاهز'
            });
        };

        // دالة مساعدة لتحديث البيانات قبل الطباعة
        vm.updatePrintData = function() {
            // التأكد من أن البيانات محدثة
            if (vm.selectedRequest) {
                console.log('تحديث بيانات الطباعة:', {
                    RequestNumber: vm.selectedRequest.RequestNumber,
                    Status: vm.selectedRequest.Status,
                    StartSequence: vm.selectedRequest.StartSequence,
                    EndSequence: vm.selectedRequest.EndSequence,
                    Quantity: vm.selectedRequest.Quantity
                });
            }
        };

        // طباعة تقرير الطلب المعتمد في نافذة جديدة
        vm.printApprovedReport = function() {
            // تأكد من أن البيانات متوفرة
            if (!vm.selectedRequest) {
                Notification.error({
                    message: "لا توجد بيانات للطباعة",
                    title: 'خطأ في الطباعة'
                });
                return;
            }

            // تحديث البيانات قبل الطباعة
            vm.updatePrintData();

            try {
                // الحصول على محتوى التقرير
                var reportContent = document.getElementById('approvedRequestReportContent');
                if (!reportContent) {
                    Notification.error({
                        message: "لا يمكن العثور على محتوى التقرير",
                        title: 'خطأ في الطباعة'
                    });
                    return;
                }

                // إنشاء نافذة جديدة للطباعة
                var printWindow = window.open('', '_blank', 'width=800,height=600');

                // كتابة محتوى HTML للطباعة
                var htmlContent = `
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>تقرير الطلب المعتمد</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
                        <style>
                            body {
                                font-family: 'Cairo', Arial, sans-serif;
                                margin: 20px;
                                direction: rtl;
                            }
                            .cairo { font-family: 'Cairo', Arial, sans-serif; }
                            @media print {
                                body { margin: 0; padding: 20px; }
                                .no-print { display: none !important; }
                            }
                            .badge {
                                display: inline-block;
                                padding: 0.35em 0.65em;
                                font-size: 0.75em;
                                font-weight: 700;
                                line-height: 1;
                                color: #fff;
                                text-align: center;
                                white-space: nowrap;
                                vertical-align: baseline;
                                border-radius: 0.25rem;
                            }
                            .bg-info { background-color: #0dcaf0 !important; }
                            .bg-success { background-color: #198754 !important; }
                        </style>
                    </head>
                    <body class="cairo">
                        <div class="container-fluid">
                            ${reportContent.innerHTML}
                        </div>
                        <script>
                            window.onload = function() {
                                setTimeout(function() {
                                    window.print();
                                }, 500);
                            };
                        </script>
                    </body>
                    </html>
                `;

                printWindow.document.write(htmlContent);
                printWindow.document.close();

                Notification.success({
                    message: "تم فتح نافذة الطباعة",
                    title: 'طباعة'
                });

            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                Notification.error({
                    message: "حدث خطأ أثناء تحضير التقرير للطباعة",
                    title: 'خطأ في الطباعة'
                });
            }
        };

        // طباعة تقرير الطلب المعتمد مباشرة
        vm.printApprovedReportDirect = function() {
            // تأكد من أن البيانات متوفرة
            if (!vm.selectedRequest) {
                Notification.error({
                    message: "لا توجد بيانات للطباعة",
                    title: 'خطأ في الطباعة'
                });
                return;
            }

            // تحديث البيانات قبل الطباعة
            vm.updatePrintData();

            try {
                // الحصول على محتوى التقرير
                var reportContent = document.getElementById('approvedRequestReportContent');
                if (!reportContent) {
                    Notification.error({
                        message: "لا يمكن العثور على محتوى التقرير",
                        title: 'خطأ في الطباعة'
                    });
                    return;
                }

                // إنشاء نافذة جديدة للطباعة المباشرة
                var printWindow = window.open('', '_blank', 'width=800,height=600');

                // كتابة محتوى HTML للطباعة
                var htmlContent = `
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>تقرير الطلب المعتمد</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
                        <style>
                            body {
                                font-family: 'Cairo', Arial, sans-serif;
                                margin: 20px;
                                direction: rtl;
                            }
                            .cairo { font-family: 'Cairo', Arial, sans-serif; }
                            @media print {
                                body { margin: 0; padding: 20px; }
                                .no-print { display: none !important; }
                            }
                            .badge {
                                display: inline-block;
                                padding: 0.35em 0.65em;
                                font-size: 0.75em;
                                font-weight: 700;
                                line-height: 1;
                                color: #fff;
                                text-align: center;
                                white-space: nowrap;
                                vertical-align: baseline;
                                border-radius: 0.25rem;
                            }
                            .bg-info { background-color: #0dcaf0 !important; }
                            .bg-success { background-color: #198754 !important; }
                        </style>
                    </head>
                    <body class="cairo">
                        <div class="container-fluid">
                            ${reportContent.innerHTML}
                        </div>
                        <script>
                            window.onload = function() {
                                setTimeout(function() {
                                    window.print();
                                    setTimeout(function() {
                                        window.close();
                                    }, 1000);
                                }, 500);
                            };
                        </script>
                    </body>
                    </html>
                `;

                printWindow.document.write(htmlContent);
                printWindow.document.close();

                Notification.success({
                    message: "تم فتح نافذة الطباعة المباشرة",
                    title: 'طباعة مباشرة'
                });

            } catch (error) {
                console.error('خطأ في الطباعة المباشرة:', error);
                Notification.error({
                    message: "حدث خطأ أثناء الطباعة المباشرة",
                    title: 'خطأ في الطباعة'
                });
            }
        };

        // دالة طباعة تقرير الطلب المنفذ
        vm.printExecutedRequestReport = function() {
            // التحقق من أن الطلب منفذ
            if (!vm.selectedRequest || vm.selectedRequest.Status !== 5) {
                Notification.error({
                    message: "يمكن طباعة التنفيذ للطلبات المنفذة فقط",
                    title: 'خطأ في الطباعة'
                });
                return;
            }

            // تعيين التاريخ الحالي
            vm.currentDate = new Date();

            // تحديث البيانات للتأكد من أنها محدثة
            vm.updatePrintData();

            // فتح مودال التقرير مع تأخير بسيط للسماح لـ Angular بتحديث البيانات
            setTimeout(function() {
                $('#executedRequestReportModal').modal('show');
            }, 100);

            // إظهار رسالة للمستخدم
            Notification.success({
                message: "تم تحضير تقرير الطلب المنفذ للطباعة",
                title: 'تقرير جاهز'
            });
        };

        // طباعة تقرير الطلب المنفذ في نافذة جديدة
        vm.printExecutedReport = function() {
            // تأكد من أن البيانات متوفرة
            if (!vm.selectedRequest) {
                Notification.error({
                    message: "لا توجد بيانات للطباعة",
                    title: 'خطأ في الطباعة'
                });
                return;
            }

            // تحديث البيانات قبل الطباعة
            vm.updatePrintData();

            try {
                // الحصول على محتوى التقرير
                var reportContent = document.getElementById('executedRequestReportContent');
                if (!reportContent) {
                    Notification.error({
                        message: "لا يمكن العثور على محتوى التقرير",
                        title: 'خطأ في الطباعة'
                    });
                    return;
                }

                // إنشاء نافذة جديدة للطباعة
                var printWindow = window.open('', '_blank', 'width=800,height=600');

                // كتابة محتوى HTML للطباعة
                var htmlContent = `
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>تقرير الطلب المنفذ</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
                        <style>
                            body {
                                font-family: 'Cairo', Arial, sans-serif;
                                margin: 20px;
                                direction: rtl;
                            }
                            .cairo { font-family: 'Cairo', Arial, sans-serif; }
                            @media print {
                                body { margin: 0; padding: 20px; }
                                .no-print { display: none !important; }
                            }
                            .badge {
                                display: inline-block;
                                padding: 0.35em 0.65em;
                                font-size: 0.75em;
                                font-weight: 700;
                                line-height: 1;
                                color: #fff;
                                text-align: center;
                                white-space: nowrap;
                                vertical-align: baseline;
                                border-radius: 0.25rem;
                            }
                            .bg-info { background-color: #0dcaf0 !important; }
                            .bg-success { background-color: #198754 !important; }
                            .bg-purple { background-color: #6f42c1 !important; }
                            .text-purple { color: #6f42c1 !important; }
                        </style>
                    </head>
                    <body class="cairo">
                        <div class="container-fluid">
                            ${reportContent.innerHTML}
                        </div>
                        <script>
                            window.onload = function() {
                                setTimeout(function() {
                                    window.print();
                                }, 500);
                            };
                        </script>
                    </body>
                    </html>
                `;

                printWindow.document.write(htmlContent);
                printWindow.document.close();

                Notification.success({
                    message: "تم فتح نافذة الطباعة",
                    title: 'طباعة'
                });

            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                Notification.error({
                    message: "حدث خطأ أثناء تحضير التقرير للطباعة",
                    title: 'خطأ في الطباعة'
                });
            }
        };

        // طباعة تقرير الطلب المنفذ مباشرة
        vm.printExecutedReportDirect = function() {
            // تأكد من أن البيانات متوفرة
            if (!vm.selectedRequest) {
                Notification.error({
                    message: "لا توجد بيانات للطباعة",
                    title: 'خطأ في الطباعة'
                });
                return;
            }

            // تحديث البيانات قبل الطباعة
            vm.updatePrintData();

            try {
                // الحصول على محتوى التقرير
                var reportContent = document.getElementById('executedRequestReportContent');
                if (!reportContent) {
                    Notification.error({
                        message: "لا يمكن العثور على محتوى التقرير",
                        title: 'خطأ في الطباعة'
                    });
                    return;
                }

                // إنشاء نافذة جديدة للطباعة المباشرة
                var printWindow = window.open('', '_blank', 'width=800,height=600');

                // كتابة محتوى HTML للطباعة
                var htmlContent = `
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>تقرير الطلب المنفذ</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
                        <style>
                            body {
                                font-family: 'Cairo', Arial, sans-serif;
                                margin: 20px;
                                direction: rtl;
                            }
                            .cairo { font-family: 'Cairo', Arial, sans-serif; }
                            @media print {
                                body { margin: 0; padding: 20px; }
                                .no-print { display: none !important; }
                            }
                            .badge {
                                display: inline-block;
                                padding: 0.35em 0.65em;
                                font-size: 0.75em;
                                font-weight: 700;
                                line-height: 1;
                                color: #fff;
                                text-align: center;
                                white-space: nowrap;
                                vertical-align: baseline;
                                border-radius: 0.25rem;
                            }
                            .bg-info { background-color: #0dcaf0 !important; }
                            .bg-success { background-color: #198754 !important; }
                            .bg-purple { background-color: #6f42c1 !important; }
                            .text-purple { color: #6f42c1 !important; }
                        </style>
                    </head>
                    <body class="cairo">
                        <div class="container-fluid">
                            ${reportContent.innerHTML}
                        </div>
                        <script>
                            window.onload = function() {
                                setTimeout(function() {
                                    window.print();
                                    setTimeout(function() {
                                        window.close();
                                    }, 1000);
                                }, 500);
                            };
                        </script>
                    </body>
                    </html>
                `;

                printWindow.document.write(htmlContent);
                printWindow.document.close();

                Notification.success({
                    message: "تم فتح نافذة الطباعة المباشرة",
                    title: 'طباعة مباشرة'
                });

            } catch (error) {
                console.error('خطأ في الطباعة المباشرة:', error);
                Notification.error({
                    message: "حدث خطأ أثناء الطباعة المباشرة",
                    title: 'خطأ في الطباعة'
                });
            }
        };
    }
})();