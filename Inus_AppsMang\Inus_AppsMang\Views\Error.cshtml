@{
    ViewBag.Title = "خطأ في النظام";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        خطأ في النظام
                    </h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        عذراً، حدث خطأ غير متوقع في النظام.
                    </p>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <button class="btn btn-secondary" onclick="history.back()">
                            <i class="bi bi-arrow-left me-2"></i>رجوع
                        </button>
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-2"></i>إعادة تحميل
                        </button>
                        <button class="btn btn-info" onclick="window.location.href='/'">
                            <i class="bi bi-house me-2"></i>الرئيسية
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
